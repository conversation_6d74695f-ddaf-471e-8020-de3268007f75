"""
PostgreSQL-based checkpoint configuration for LangGraph
Production-ready persistent checkpointing
"""
import os
from typing import Optional
from langgraph_checkpoint_postgres.aio import AsyncPostgresSaver
from langgraph_checkpoint_postgres import PostgresSaver
from langgraph.checkpoint.memory import MemorySaver
import logging

logger = logging.getLogger(__name__)

class CheckpointConfig:
    """Configuration for LangGraph checkpointing"""

    def __init__(self):
        self.postgres_url = os.getenv("POSTGRES_CHECKPOINT_URL")
        self.use_postgres = bool(self.postgres_url)
        self._memory_checkpointer: Optional[MemorySaver] = None
        self._setup_done = False
    
    async def get_async_checkpointer(self) -> AsyncPostgresSaver:
        """Get async PostgreSQL checkpointer"""
        if not self.use_postgres:
            raise ValueError("PostgreSQL URL not configured. Set POSTGRES_CHECKPOINT_URL environment variable.")

        try:
            # Create a new checkpointer instance each time
            # This is the recommended approach for the new API
            checkpointer = AsyncPostgresSaver.from_conn_string(
                self.postgres_url,
                # Optional: customize table name
                # table_name="langgraph_checkpoints"
            )

            # Setup the database tables only once
            if not self._setup_done:
                async with checkpointer as setup_checkpointer:
                    await setup_checkpointer.setup()
                    self._setup_done = True
                    logger.info("✅ PostgreSQL async checkpointer initialized and tables setup")

            return checkpointer

        except Exception as e:
            logger.error(f"❌ Failed to initialize PostgreSQL async checkpointer: {e}")
            raise
    
    def get_sync_checkpointer(self) -> PostgresSaver:
        """Get sync PostgreSQL checkpointer"""
        if not self.use_postgres:
            raise ValueError("PostgreSQL URL not configured. Set POSTGRES_CHECKPOINT_URL environment variable.")

        try:
            # Create a new checkpointer instance each time
            with PostgresSaver.from_conn_string(
                self.postgres_url,
                # Optional: customize table name
                # table_name="langgraph_checkpoints"
            ) as checkpointer:
                # Setup the database tables only once
                if not self._setup_done:
                    checkpointer.setup()
                    self._setup_done = True
                    logger.info("✅ PostgreSQL sync checkpointer initialized and tables setup")

                return checkpointer

        except Exception as e:
            logger.error(f"❌ Failed to initialize PostgreSQL sync checkpointer: {e}")
            raise
    
    def get_memory_checkpointer(self) -> MemorySaver:
        """Get in-memory checkpointer (fallback for development)"""
        if self._memory_checkpointer is None:
            self._memory_checkpointer = MemorySaver()
            logger.warning("⚠️  Using in-memory checkpointer. For production, configure POSTGRES_CHECKPOINT_URL.")
        
        return self._memory_checkpointer
    
    async def get_checkpointer(self):
        """Get the appropriate checkpointer based on configuration"""
        if self.use_postgres:
            return await self.get_async_checkpointer()
        else:
            return self.get_memory_checkpointer()
    
    async def close(self):
        """Close checkpointer connections"""
        # With the new API, connections are managed by context managers
        # No explicit cleanup needed
        logger.info("✅ Checkpoint configuration closed")

# Global checkpoint config instance
checkpoint_config = CheckpointConfig()

async def get_checkpointer():
    """Get the configured checkpointer"""
    return await checkpoint_config.get_checkpointer()

async def close_checkpointer():
    """Close checkpointer connections"""
    await checkpoint_config.close()

# Environment variable examples for .env file:
"""
# For PostgreSQL checkpointing (recommended for production)
POSTGRES_CHECKPOINT_URL=postgresql://username:password@localhost:5432/langgraph_checkpoints

# Alternative formats:
# POSTGRES_CHECKPOINT_URL=postgresql://user:pass@localhost/dbname
# POSTGRES_CHECKPOINT_URL=postgresql://user:pass@localhost:5432/dbname?sslmode=require

# For development without PostgreSQL, leave this unset to use in-memory checkpointing
"""
