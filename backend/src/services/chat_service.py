"""
Organized Chat Service Module with PostgreSQL Memory

This module provides an agentic chatbot service with the following capabilities:
- Database search using Qdrant vector store
- Appointment booking system
- Multi-language support (English, Nepali, Romanized Nepali)
- PostgreSQL-based persistent memory across conversations
- Async conversation management
"""

# Standard library imports
import os
import asyncio
import uuid
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional, Dict, Any

# Third-party imports
from dotenv import load_dotenv

# LangChain imports
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.tools import tool
from langchain_openai import OpenAIEmbeddings
from langchain.chains import create_retrieval_chain
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage

# LangGraph imports for PostgreSQL checkpointing
from langgraph.prebuilt import create_react_agent
from langgraph.checkpoint.postgres.aio import AsyncPostgresSaver

# Qdrant imports
from qdrant_client import QdrantClient
from langchain_qdrant import QdrantVectorStore, RetrievalMode

# Local imports
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from config.checkpoint_config import CheckpointConfig

# Load environment variables
load_dotenv()
# =============================================================================
# CONFIGURATION SECTION
# =============================================================================

class ChatServiceConfig:
    """Configuration class for chat service settings"""

    # Model settings
    MODEL_NAME = "gemini-1.5-flash"
    MODEL_TEMPERATURE = 0.1

    # Embedding settings
    EMBEDDING_MODEL = "text-embedding-3-large"
    EMBEDDING_DIMENSIONS = 1536

    # Qdrant settings
    QDRANT_HOST = "*************"
    QDRANT_PORT = 6333
    COLLECTION_NAME = "langsmit_test"

    # Search settings
    SEARCH_TYPE = "mmr"
    SEARCH_K = 5


# =============================================================================
# MODEL AND VECTOR STORE INITIALIZATION
# =============================================================================

def initialize_models():
    """Initialize and return all required models and stores"""

    # Setup Gemini model
    model = ChatGoogleGenerativeAI(
        model=ChatServiceConfig.MODEL_NAME,
        temperature=ChatServiceConfig.MODEL_TEMPERATURE,
        google_api_key=os.getenv("GOOGLE_API_KEY")
    )

    # Setup embeddings for Qdrant
    embeddings = OpenAIEmbeddings(
        model=ChatServiceConfig.EMBEDDING_MODEL,
        api_key=os.getenv("OPENAI_API_KEY"),
        dimensions=ChatServiceConfig.EMBEDDING_DIMENSIONS
    )

    # Initialize Qdrant client
    qdrant_client = QdrantClient(
        host=ChatServiceConfig.QDRANT_HOST,
        port=ChatServiceConfig.QDRANT_PORT,
    )

    # Setup vector store
    vector_store = QdrantVectorStore(
        client=qdrant_client,
        collection_name=ChatServiceConfig.COLLECTION_NAME,
        embedding=embeddings,
        retrieval_mode=RetrievalMode.DENSE,
        content_payload_key="page_content",
        metadata_payload_key="metadata"
    )

    # Setup retriever and QA chain
    retriever = vector_store.as_retriever(
        search_type=ChatServiceConfig.SEARCH_TYPE,
        search_kwargs={"k": ChatServiceConfig.SEARCH_K}
    )

    qa_chain = create_retrieval_chain(model, retriever)

    return model, vector_store, retriever, qa_chain

# Initialize all models and stores
model, vector_store, retriever, qa_chain = initialize_models()


# =============================================================================
# POSTGRESQL MEMORY MANAGEMENT
# =============================================================================

class PostgreSQLMemoryManager:
    """Manages conversation memory using PostgreSQL"""

    def __init__(self):
        self.checkpoint_config = CheckpointConfig()
        self._checkpointer = None

    async def get_checkpointer(self):
        """Get or create PostgreSQL checkpointer"""
        if self._checkpointer is None:
            try:
                self._checkpointer = await self.checkpoint_config.get_async_checkpointer()
                print("✅ PostgreSQL memory initialized successfully")
            except Exception as e:
                print(f"⚠️  PostgreSQL not available, using memory fallback: {e}")
                self._checkpointer = self.checkpoint_config.get_memory_checkpointer()
        return self._checkpointer

    async def save_conversation_state(self, thread_id: str, state: Dict[str, Any]):
        """Save conversation state to PostgreSQL"""
        checkpointer = await self.get_checkpointer()
        # Implementation depends on your specific state structure
        pass

    async def load_conversation_state(self, thread_id: str) -> Dict[str, Any]:
        """Load conversation state from PostgreSQL"""
        checkpointer = await self.get_checkpointer()
        # Implementation depends on your specific state structure
        return {}

    async def clear_conversation(self, thread_id: str):
        """Clear conversation history for a specific thread"""
        checkpointer = await self.get_checkpointer()
        # Clear the conversation state
        pass

# Initialize memory manager
memory_manager = PostgreSQLMemoryManager()

# =============================================================================
# DATA MODELS AND STORAGE
# =============================================================================

class AppointmentManager:
    """Manages appointment slots and bookings"""

    def __init__(self):
        self.appointment_slots = []
        self.booked_appointments = []
        self._initialize_slots()

    def _initialize_slots(self):
        """Initialize available appointment slots"""
        base_date = datetime.now().replace(hour=9, minute=0, second=0, microsecond=0)
        for i in range(14):  # Next 14 days
            date = base_date + timedelta(days=i)
            for hour in [9, 11, 14, 16]:  # Available slots: 9AM, 11AM, 2PM, 4PM
                self.appointment_slots.append({
                    "date": date.replace(hour=hour).strftime("%Y-%m-%d"),
                    "time": date.replace(hour=hour).strftime("%H:%M"),
                    "available": True
                })

    def get_available_slots(self, days_ahead: int = 14):
        """Get available appointment slots"""
        available_slots = []
        base_date = datetime.now().replace(hour=9, minute=0, second=0, microsecond=0)

        for i in range(1, days_ahead + 1):  # Start from tomorrow
            date = base_date + timedelta(days=i)
            # Skip weekends
            if date.weekday() < 5:  # Monday=0, Friday=4
                for hour in [9, 11, 14, 16]:  # 9AM, 11AM, 2PM, 4PM
                    slot_datetime = date.replace(hour=hour)
                    slot_info = {
                        "date": slot_datetime.strftime("%Y-%m-%d"),
                        "time": slot_datetime.strftime("%H:%M"),
                        "day": slot_datetime.strftime("%A"),
                        "formatted": slot_datetime.strftime("%B %d, %Y at %I:%M %p")
                    }
                    available_slots.append(slot_info)
        return available_slots

    def book_appointment(self, name: str, email: str, phone: str, date: str, time: str, service_type: str):
        """Book an appointment and return confirmation"""
        # Check if the slot is available
        requested_slot = None
        for slot in self.appointment_slots:
            if slot["date"] == date and slot["time"] == time:
                requested_slot = slot
                break

        if not requested_slot:
            return f"Error: No appointment slot available for {date} at {time}. Please check available slots."

        if not requested_slot["available"]:
            return f"Error: The slot on {date} at {time} is already booked. Please choose another time."

        # Book the appointment
        appointment_id = f"APT-{len(self.booked_appointments) + 1:04d}"
        appointment = {
            "id": appointment_id,
            "name": name,
            "email": email,
            "phone": phone,
            "date": date,
            "time": time,
            "service_type": service_type,
            "status": "confirmed"
        }

        self.booked_appointments.append(appointment)
        requested_slot["available"] = False

        return f"""
Appointment Successfully Booked!

Appointment ID: {appointment_id}
Name: {name}
Email: {email}
Phone: {phone}
Date: {date}
Time: {time}
Service: {service_type}
Status: Confirmed

Please save your appointment ID for future reference.
A confirmation email will be sent to {email}.
"""

# Initialize appointment manager
appointment_manager = AppointmentManager()

# =============================================================================
# TOOL FUNCTIONS
# =============================================================================

@tool
def search_database(query: str) -> str:
    """
    Search the database for products or services using Qdrant vector search.

    Args:
        query: The search term to look for

    Returns:
        A formatted string with search results
    """
    try:
        answer = qa_chain.invoke({"question": query})
        result = answer["result"]
        return result
    except Exception as e:
        return f"Error searching database: {str(e)}"


@tool
def get_current_date() -> str:
    """
    Get current date and time information.

    Returns:
        Current date and time details
    """
    now = datetime.now()
    return f"""📅 Current Date Information:
• Date: {now.strftime('%Y-%m-%d')}
• Time: {now.strftime('%H:%M:%S')}
• Day: {now.strftime('%A')}
• Formatted: {now.strftime('%B %d, %Y at %I:%M %p')}"""


@tool
def get_available_slots(days_ahead: int = 14) -> str:
    """
    Get available appointment slots.

    Args:
        days_ahead: Number of days to look ahead

    Returns:
        List of available appointment slots
    """
    available_slots = appointment_manager.get_available_slots(days_ahead)

    if not available_slots:
        return "No available slots found."

    # Show first 10 slots
    result = "📅 Available Appointment Slots:\n\n"
    for i, slot in enumerate(available_slots[:10]):
        result += f"{i+1}. {slot['formatted']} ({slot['date']} {slot['time']})\n"

    if len(available_slots) > 10:
        result += f"\n... and {len(available_slots) - 10} more slots available."

    return result


@tool
def book_appointment(name: str, email: str, phone: str, date: str, time: str, service_type: str = "General Consultation") -> str:
    """
    Book an appointment with customer details.

    Args:
        name: Customer's full name
        email: Customer's email address
        phone: Customer's phone number
        date: Preferred date in YYYY-MM-DD format
        time: Preferred time in HH:MM format
        service_type: Type of service needed

    Returns:
        Confirmation message with appointment details
    """
    # Validate inputs
    if not all([name, email, phone, date, time]):
        return "Error: All fields (name, email, phone, date, time) are required to book an appointment."

    # Use appointment manager to book
    return appointment_manager.book_appointment(name, email, phone, date, time, service_type)

# =============================================================================
# AGENT CONFIGURATION
# =============================================================================

class AgentPrompts:
    """Contains system prompts and agent configuration"""

    SYSTEM_PROMPT = """You are a professional AI assistant for a customer service center. You MUST use the available tools to help customers.

Answer the following questions as best you can. You have access to the following tools:

{tools}

Use the following format:

Question: the input question you must answer
Thought: you should always think about what to do
Action: the action to take, should be one of [{tool_names}]
Action Input: the input to the action
Observation: the result of the action
... (this Thought/Action/Action Input/Observation can repeat N times)
Thought: I now know the final answer
Final Answer: the final answer to the original input question

CRITICAL RULES FOR TOOL USAGE:
- For ANY question about products, services, apps, troubleshooting, or information → MUST use search_database tool
- For appointment booking → MUST use get_current_date, then get_available_slots, then book_appointment
- For date/time questions → MUST use get_current_date
- For availability questions → MUST use get_available_slots
- NEVER answer informational questions without using search_database first
- Simple greetings like "Hello", "Hi", "Good morning" don't need tools

MANDATORY TOOL USAGE EXAMPLES:
- "can i book an appointment" → Action: search_database, Action Input: "appointment booking services"
- "my app is not working" → Action: search_database, Action Input: "app troubleshooting not working"
- "what services do you offer" → Action: search_database, Action Input: "services offered"
- "any available slots" → Action: get_available_slots, Action Input: 14
- "what's today's date" → Action: get_current_date, Action Input:

The message can be in Nepali (नेपाली), Romanized Nepali, English, or a mix of both.
Respond in clear English or Romanized Nepali based on user's language preference.

Begin!

Question: {input}
Thought: {agent_scratchpad}"""


async def create_agentic_chatbot_with_memory():
    """Create and configure the agentic chatbot with PostgreSQL memory"""
    from langchain_core.prompts import PromptTemplate

    # Create list of available tools
    tools = [search_database, get_current_date, get_available_slots, book_appointment]

    # Create the ReAct prompt template
    prompt = PromptTemplate.from_template(AgentPrompts.SYSTEM_PROMPT)

    # Get PostgreSQL checkpointer
    checkpointer = await memory_manager.get_checkpointer()

    # Create the LangGraph agent with PostgreSQL memory
    agent = create_react_agent(
        model=model,
        tools=tools,
        prompt=prompt,
        checkpointer=checkpointer
    )

    return agent

# Global variable to store the agent (will be initialized async)
agentic_chatbot = None

# =============================================================================
# ASYNC USER INTERFACE FUNCTIONS
# =============================================================================

class AsyncChatInterface:
    """Handles async user interface for the chatbot with PostgreSQL memory"""

    @staticmethod
    async def async_chat_interface():
        """Async interactive chat interface with persistent memory"""
        global agentic_chatbot

        # Initialize the agent with PostgreSQL memory
        if agentic_chatbot is None:
            print("🔧 Initializing chatbot with PostgreSQL memory...")
            agentic_chatbot = await create_agentic_chatbot_with_memory()
            print("✅ Chatbot initialized successfully!")

        print("=" * 60)
        print("🤖 AGENTIC CUSTOMER SERVICE CHATBOT (PostgreSQL Memory)")
        print("=" * 60)
        print("Available services:")
        print("• Search for products and services")
        print("• Book appointments")
        print("• General customer support")
        print("\nType 'quit' to exit, 'new' to start new conversation")
        print("=" * 60)

        # Generate a unique thread ID for this conversation
        thread_id = str(uuid.uuid4())
        config = {"configurable": {"thread_id": thread_id}}

        print(f"📝 Conversation ID: {thread_id}")

        while True:
            user_input = input("\n👤 You: ").strip()

            if user_input.lower() == 'quit':
                print("\n🤖 Thank you for using our service! Have a great day!")
                break
            elif user_input.lower() == 'new':
                # Start a new conversation with a new thread ID
                thread_id = str(uuid.uuid4())
                config = {"configurable": {"thread_id": thread_id}}
                print(f"\n🔄 Started new conversation: {thread_id}")
                continue
            elif not user_input:
                continue

            try:
                print("\n🤖 Assistant: ", end="", flush=True)

                # Use the agent with PostgreSQL memory
                response = await agentic_chatbot.ainvoke(
                    {"messages": [HumanMessage(content=user_input)]},
                    config=config
                )

                # Print the final response
                final_message = response["messages"][-1]
                print(final_message.content)

            except Exception as e:
                print(f"\n❌ Error: {str(e)}")
                print("Please try again or contact support.")

    @staticmethod
    def run_async_chat():
        """Run the async chat interface"""
        try:
            asyncio.run(AsyncChatInterface.async_chat_interface())
        except KeyboardInterrupt:
            print("\n\n👋 Chat session ended by user")
        except Exception as e:
            print(f"\n❌ Fatal error: {e}")
            print("Please check your PostgreSQL connection and try again.")



def main():
    """Main function to run the chat service"""
    print("🤖 AGENTIC CUSTOMER SERVICE CHATBOT")
    print("=" * 60)
    print("Available services:")
    print("• Search for products and services")
    print("• Book appointments")
    print("• General customer support")
    print("\nType 'quit' to exit")
    print("=" * 60)

    while True:
        user_input = input("\n👤 You: ").strip()

        if user_input.lower() == 'quit':
            print("\n🤖 Thank you for using our service! Have a great day!")
            break
        elif not user_input:
            continue

        try:
            print("\n🤖 Assistant: ", end="", flush=True)

            # Use the agent executor directly
            response = agentic_chatbot.invoke({"input": user_input})

            # Print the final response
            print(response["output"])

        except Exception as e:
            print(f"\n❌ Error: {str(e)}")
            print("Please try again or contact support.")


if __name__ == "__main__":
    main()
