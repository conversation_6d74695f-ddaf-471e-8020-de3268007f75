"""
Organized Chat Service Module

This module provides an agentic chatbot service with the following capabilities:
- Database search using Qdrant vector store
- Appointment booking system
- Multi-language support (English, Nepali, Romanized Nepali)
- Memory persistence across conversations
"""

# Standard library imports
import os
import uuid
from datetime import datetime, timedelta

# Third-party imports
from dotenv import load_dotenv

# LangChain imports
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.tools import tool
from langchain_core.messages import HumanMessage
from langchain_openai import OpenAIEmbeddings
from langchain.chains import create_retrieval_chain

# LangGraph imports
from langgraph.prebuilt import create_react_agent
from langgraph.checkpoint.memory import MemorySaver

# Qdrant imports
from qdrant_client import QdrantClient
from langchain_qdrant import QdrantVectorStore, RetrievalMode

# Load environment variables
load_dotenv()
# =============================================================================
# CONFIGURATION SECTION
# =============================================================================

class ChatServiceConfig:
    """Configuration class for chat service settings"""

    # Model settings
    MODEL_NAME = "gemini-1.5-flash"
    MODEL_TEMPERATURE = 0.1

    # Embedding settings
    EMBEDDING_MODEL = "text-embedding-3-large"
    EMBEDDING_DIMENSIONS = 1536

    # Qdrant settings
    QDRANT_HOST = "*************"
    QDRANT_PORT = 6333
    COLLECTION_NAME = "langsmit_test"

    # Search settings
    SEARCH_TYPE = "mmr"
    SEARCH_K = 5


# =============================================================================
# MODEL AND VECTOR STORE INITIALIZATION
# =============================================================================

def initialize_models():
    """Initialize and return all required models and stores"""

    # Setup Gemini model
    model = ChatGoogleGenerativeAI(
        model=ChatServiceConfig.MODEL_NAME,
        temperature=ChatServiceConfig.MODEL_TEMPERATURE,
        google_api_key=os.getenv("GOOGLE_API_KEY")
    )

    # Setup embeddings for Qdrant
    embeddings = OpenAIEmbeddings(
        model=ChatServiceConfig.EMBEDDING_MODEL,
        api_key=os.getenv("OPENAI_API_KEY"),
        dimensions=ChatServiceConfig.EMBEDDING_DIMENSIONS
    )

    # Initialize Qdrant client
    qdrant_client = QdrantClient(
        host=ChatServiceConfig.QDRANT_HOST,
        port=ChatServiceConfig.QDRANT_PORT,
    )

    # Setup vector store
    vector_store = QdrantVectorStore(
        client=qdrant_client,
        collection_name=ChatServiceConfig.COLLECTION_NAME,
        embedding=embeddings,
        retrieval_mode=RetrievalMode.DENSE,
        content_payload_key="page_content",
        metadata_payload_key="metadata"
    )

    # Setup retriever and QA chain
    retriever = vector_store.as_retriever(
        search_type=ChatServiceConfig.SEARCH_TYPE,
        search_kwargs={"k": ChatServiceConfig.SEARCH_K}
    )

    qa_chain = create_retrieval_chain(model, retriever)

    return model, vector_store, retriever, qa_chain

# Initialize all models and stores
model, vector_store, retriever, qa_chain = initialize_models()

# =============================================================================
# DATA MODELS AND STORAGE
# =============================================================================

class AppointmentManager:
    """Manages appointment slots and bookings"""

    def __init__(self):
        self.appointment_slots = []
        self.booked_appointments = []
        self._initialize_slots()

    def _initialize_slots(self):
        """Initialize available appointment slots"""
        base_date = datetime.now().replace(hour=9, minute=0, second=0, microsecond=0)
        for i in range(14):  # Next 14 days
            date = base_date + timedelta(days=i)
            for hour in [9, 11, 14, 16]:  # Available slots: 9AM, 11AM, 2PM, 4PM
                self.appointment_slots.append({
                    "date": date.replace(hour=hour).strftime("%Y-%m-%d"),
                    "time": date.replace(hour=hour).strftime("%H:%M"),
                    "available": True
                })

    def get_available_slots(self, days_ahead: int = 14):
        """Get available appointment slots"""
        available_slots = []
        base_date = datetime.now().replace(hour=9, minute=0, second=0, microsecond=0)

        for i in range(1, days_ahead + 1):  # Start from tomorrow
            date = base_date + timedelta(days=i)
            # Skip weekends
            if date.weekday() < 5:  # Monday=0, Friday=4
                for hour in [9, 11, 14, 16]:  # 9AM, 11AM, 2PM, 4PM
                    slot_datetime = date.replace(hour=hour)
                    slot_info = {
                        "date": slot_datetime.strftime("%Y-%m-%d"),
                        "time": slot_datetime.strftime("%H:%M"),
                        "day": slot_datetime.strftime("%A"),
                        "formatted": slot_datetime.strftime("%B %d, %Y at %I:%M %p")
                    }
                    available_slots.append(slot_info)
        return available_slots

    def book_appointment(self, name: str, email: str, phone: str, date: str, time: str, service_type: str):
        """Book an appointment and return confirmation"""
        # Check if the slot is available
        requested_slot = None
        for slot in self.appointment_slots:
            if slot["date"] == date and slot["time"] == time:
                requested_slot = slot
                break

        if not requested_slot:
            return f"Error: No appointment slot available for {date} at {time}. Please check available slots."

        if not requested_slot["available"]:
            return f"Error: The slot on {date} at {time} is already booked. Please choose another time."

        # Book the appointment
        appointment_id = f"APT-{len(self.booked_appointments) + 1:04d}"
        appointment = {
            "id": appointment_id,
            "name": name,
            "email": email,
            "phone": phone,
            "date": date,
            "time": time,
            "service_type": service_type,
            "status": "confirmed"
        }

        self.booked_appointments.append(appointment)
        requested_slot["available"] = False

        return f"""
Appointment Successfully Booked!

Appointment ID: {appointment_id}
Name: {name}
Email: {email}
Phone: {phone}
Date: {date}
Time: {time}
Service: {service_type}
Status: Confirmed

Please save your appointment ID for future reference.
A confirmation email will be sent to {email}.
"""

# Initialize appointment manager
appointment_manager = AppointmentManager()

# =============================================================================
# TOOL FUNCTIONS
# =============================================================================

@tool
def search_database(query: str) -> str:
    """
    Search the database for products or services using Qdrant vector search.

    Args:
        query: The search term to look for

    Returns:
        A formatted string with search results
    """
    try:
        answer = qa_chain.invoke({"question": query})
        result = answer["result"]
        return result
    except Exception as e:
        return f"Error searching database: {str(e)}"


@tool
def get_current_date() -> str:
    """
    Get current date and time information.

    Returns:
        Current date and time details
    """
    now = datetime.now()
    return f"""📅 Current Date Information:
• Date: {now.strftime('%Y-%m-%d')}
• Time: {now.strftime('%H:%M:%S')}
• Day: {now.strftime('%A')}
• Formatted: {now.strftime('%B %d, %Y at %I:%M %p')}"""


@tool
def get_available_slots(days_ahead: int = 14) -> str:
    """
    Get available appointment slots.

    Args:
        days_ahead: Number of days to look ahead

    Returns:
        List of available appointment slots
    """
    available_slots = appointment_manager.get_available_slots(days_ahead)

    if not available_slots:
        return "No available slots found."

    # Show first 10 slots
    result = "📅 Available Appointment Slots:\n\n"
    for i, slot in enumerate(available_slots[:10]):
        result += f"{i+1}. {slot['formatted']} ({slot['date']} {slot['time']})\n"

    if len(available_slots) > 10:
        result += f"\n... and {len(available_slots) - 10} more slots available."

    return result


@tool
def book_appointment(name: str, email: str, phone: str, date: str, time: str, service_type: str = "General Consultation") -> str:
    """
    Book an appointment with customer details.

    Args:
        name: Customer's full name
        email: Customer's email address
        phone: Customer's phone number
        date: Preferred date in YYYY-MM-DD format
        time: Preferred time in HH:MM format
        service_type: Type of service needed

    Returns:
        Confirmation message with appointment details
    """
    # Validate inputs
    if not all([name, email, phone, date, time]):
        return "Error: All fields (name, email, phone, date, time) are required to book an appointment."

    # Use appointment manager to book
    return appointment_manager.book_appointment(name, email, phone, date, time, service_type)

# =============================================================================
# AGENT CONFIGURATION
# =============================================================================

class AgentPrompts:
    """Contains system prompts and agent configuration"""

    SYSTEM_PROMPT = """You are a professional AI assistant for a customer service center. You MUST use the available tools to help customers.

The message can be in Nepali (नेपाली), Romanized Nepali, English, or a mix of both.

AVAILABLE TOOLS:
1. **search_database**: Use this tool for ANY question or queries about products, services, apps, or information
2. **get_current_date**: Use this tool to get current date and time (MANDATORY for booking processes)
3. **get_available_slots**: Use this tool to show available appointment slots
4. **book_appointment**: Use this tool to schedule appointments

CRITICAL RULES:
- MANDATORY: Use search_database tool for ANY question that needs information (apps, troubleshooting, products, services, "how to", etc.)
- NEVER answer informational questions without using search_database tool first
- Questions like "my app is not working", "how to download", "what is", "I need help with" → MUST use search_database
- For booking: ALWAYS get current date first, then show available slots, then book
- Use get_current_date tool when user asks about dates or for booking processes
- Use get_available_slots when user asks "any available slot", "when can I book", etc.
- Use relevant keywords from user's question for search
- Handle Nepali, Romanized Nepali, and English input
- Respond in clear English or Romanized Nepali
- Do NOT show tool calls in response - only show the final clean answer
- Only simple greetings like "Hello" don't need tools

BOOKING PROCESS:
1. Search for service/course info if needed
2. Get current date (MANDATORY)
3. Show available slots
4. Collect customer details
5. Book appointment

EXAMPLES OF MANDATORY TOOL USAGE:
- "my app is not working" → search_database("app not working troubleshooting")
- "any available slot" → get_available_slots()
- "what's today's date" → get_current_date()
- "book Loksewa" → search_database("Loksewa") → get_current_date() → get_available_slots()

IMPORTANT:
- ALWAYS search first, then provide clean response without showing tool usage
- Response should be helpful and in user's preferred language style
"""


def create_agentic_chatbot():
    """Create and configure the agentic chatbot"""

    # Create list of available tools
    tools = [search_database, get_current_date, get_available_slots, book_appointment]

    # Create memory for conversation persistence
    checkpointer = MemorySaver()

    # Create the agentic chatbot using LangGraph
    return create_react_agent(
        model=model,
        tools=tools,
        prompt=AgentPrompts.SYSTEM_PROMPT,
        checkpointer=checkpointer
    )

# Initialize the chatbot
agentic_chatbot = create_agentic_chatbot()

# =============================================================================
# USER INTERFACE FUNCTIONS
# =============================================================================

class ChatInterface:
    """Handles user interface for the chatbot"""

    @staticmethod
    def chat_interface():
        """Interactive chat interface for the agentic chatbot"""
        print("=" * 60)
        print("🤖 AGENTIC CUSTOMER SERVICE CHATBOT")
        print("=" * 60)
        print("Available services:")
        print("• Search for products and services")
        print("• Book appointments")
        print("• General customer support")
        print("\nType 'quit' to exit, 'new' to start a new conversation")
        print("=" * 60)

        # Create a unique thread for this conversation
        config = {"configurable": {"thread_id": str(uuid.uuid4())}}

        while True:
            user_input = input("\n👤 You: ").strip()

            if user_input.lower() == 'quit':
                print("\n🤖 Thank you for using our service! Have a great day!")
                break
            elif user_input.lower() == 'new':
                config = {"configurable": {"thread_id": str(uuid.uuid4())}}
                print("\n🤖 Starting a new conversation...")
                continue
            elif not user_input:
                continue

            try:
                # Send message to the agentic chatbot
                user_message = {"messages": [HumanMessage(user_input)]}

                print("\n🤖 Assistant: ", end="", flush=True)

                # First try simple invoke
                response = agentic_chatbot.invoke(user_message, config=config)

                # Check if there were any tool calls in the conversation
                tool_used = False
                for message in response["messages"]:
                    if hasattr(message, 'tool_calls') and message.tool_calls:
                        tool_used = True
                        print(f"🔧 Used tool: {message.tool_calls[0]['name']} ")

                # Print the final response
                final_message = response["messages"][-1]
                print(final_message.content)

                if not tool_used and any(keyword in user_input.lower() for keyword in ['app', 'download', 'product', 'service', 'search', 'find']):
                    print("\n⚠️  Note: No tools were used. The agent should have used search_database for this query.")

            except Exception as e:
                print(f"\n❌ Error: {str(e)}")
                print("Please try again or contact support.")

    @staticmethod
    def demo_conversation():
        """Run a demo conversation to show the chatbot capabilities"""
        print("\n" + "=" * 60)
        print("🎯 DEMO CONVERSATION")
        print("=" * 60)

        demo_inputs = [
            "Hello! I'm looking for a laptop",
            "Can you search for gaming products?",
            "I'd like to book an appointment for tech support",
            "My name is John Smith, <NAME_EMAIL>, phone is 555-0123",
            "I'd like to schedule for tomorrow at 2PM for technical support consultation"
        ]

        config = {"configurable": {"thread_id": str(uuid.uuid4())}}

        for user_input in demo_inputs:
            print(f"\n👤 User: {user_input}")

            try:
                user_message = {"messages": [HumanMessage(user_input)]}
                response = agentic_chatbot.invoke(user_message, config=config)
                print(f"🤖 Assistant: {response['messages'][-1].content}")
                print("-" * 40)

            except Exception as e:
                print(f"❌ Error: {str(e)}")


# =============================================================================
# TESTING AND DEBUGGING FUNCTIONS
# =============================================================================

class TestSuite:
    """Contains testing functions for the chat service"""

    @staticmethod
    def test_tools():
        """Test the tools directly"""
        print("\n🧪 Testing components...")

        # Get tools list
        tools_list = [search_database, get_current_date, get_available_slots, book_appointment]

        # Test basic model
        print("\n1. Testing basic model:")
        try:
            basic_response = model.invoke("Hello, can you respond?")
            print(f"✅ Model works: {basic_response.content}")
        except Exception as e:
            print(f"❌ Model error: {e}")
            return

        # Test search tool
        print("\n2. Testing search_database tool:")
        try:
            result = search_database("app")
            print(f"✅ Search tool result: {result}")
        except Exception as e:
            print(f"❌ Search tool error: {e}")

        # Test if tools are properly bound to model
        print("\n3. Testing model with tools:")
        try:
            model_with_tools = model.bind_tools(tools_list)
            response = model_with_tools.invoke("Search for information about apps")
            print(f"✅ Model response: {response.content}")
            if hasattr(response, 'tool_calls') and response.tool_calls:
                print(f"✅ Tool calls detected: {response.tool_calls}")
            else:
                print("⚠️  No tool calls made")
        except Exception as e:
            print(f"❌ Model with tools error: {e}")

        # Test agent
        print("\n4. Testing agent:")
        try:
            config = {"configurable": {"thread_id": "test-thread"}}
            user_message = {"messages": [HumanMessage("Hello")]}
            response = agentic_chatbot.invoke(user_message, config=config)
            print(f"✅ Agent works: {response['messages'][-1].content}")
        except Exception as e:
            print(f"❌ Agent error: {e}")

# =============================================================================
# MAIN EXECUTION
# =============================================================================

def main():
    """Main function to run the chat service"""
    print("Choose an option:")
    print("1. Interactive Chat")
    print("2. Demo Conversation")
    print("3. Test Tools")

    choice = input("Enter your choice (1, 2, or 3): ").strip()

    if choice == "1":
        ChatInterface.chat_interface()
    elif choice == "2":
        ChatInterface.demo_conversation()
    elif choice == "3":
        TestSuite.test_tools()
    else:
        print("Invalid choice. Starting interactive chat...")
        ChatInterface.chat_interface()


if __name__ == "__main__":
    main()
