#!/usr/bin/env python3
"""
Quick setup script for PostgreSQL memory in chat service
"""
import os
import subprocess
import sys
from pathlib import Path

def check_docker():
    """Check if Docker is installed and running"""
    try:
        result = subprocess.run(['docker', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Docker is installed")
            return True
    except FileNotFoundError:
        pass
    
    print("❌ Docker is not installed or not in PATH")
    print("💡 Please install Docker first: https://docs.docker.com/get-docker/")
    return False

def start_postgres():
    """Start PostgreSQL container"""
    print("🐳 Starting PostgreSQL container...")
    
    try:
        # Stop existing container if running
        subprocess.run(['docker', 'stop', 'langgraph-postgres'], 
                      capture_output=True, check=False)
        subprocess.run(['docker', 'rm', 'langgraph-postgres'], 
                      capture_output=True, check=False)
        
        # Start new container
        cmd = [
            'docker', 'run', '--name', 'langgraph-postgres',
            '-e', 'POSTGRES_DB=langgraph_checkpoints',
            '-e', 'POSTGRES_USER=testuser',
            '-e', 'POSTGRES_PASSWORD=testpass123',
            '-p', '5432:5432',
            '-d', 'postgres:15-alpine'
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ PostgreSQL container started successfully")
            return True
        else:
            print(f"❌ Failed to start PostgreSQL: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Error starting PostgreSQL: {e}")
        return False

def setup_env_file():
    """Setup .env file with PostgreSQL URL"""
    env_file = Path('.env')
    env_example = Path('.env.example')
    
    if not env_file.exists() and env_example.exists():
        print("📝 Creating .env file from .env.example...")
        with open(env_example, 'r') as src, open(env_file, 'w') as dst:
            content = src.read()
            # Uncomment PostgreSQL URL
            content = content.replace(
                '# POSTGRES_CHECKPOINT_URL=postgresql://testuser:testpass123@localhost:5432/langgraph_checkpoints',
                'POSTGRES_CHECKPOINT_URL=postgresql://testuser:testpass123@localhost:5432/langgraph_checkpoints'
            )
            dst.write(content)
        print("✅ .env file created")
    else:
        print("📝 .env file already exists")

def setup_postgres_tables():
    """Setup PostgreSQL tables for LangGraph"""
    print("🔧 Setting up PostgreSQL tables...")
    
    try:
        # Run the setup script
        result = subprocess.run([
            sys.executable, 'scripts/setup_postgres_checkpoints.py'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ PostgreSQL tables setup successfully")
            return True
        else:
            print(f"❌ Failed to setup tables: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Error setting up tables: {e}")
        return False

def main():
    """Main setup function"""
    print("🚀 Setting up PostgreSQL Memory for Chat Service")
    print("=" * 60)
    
    # Check prerequisites
    if not check_docker():
        return False
    
    # Start PostgreSQL
    if not start_postgres():
        return False
    
    # Setup environment
    setup_env_file()
    
    # Wait a bit for PostgreSQL to start
    print("⏳ Waiting for PostgreSQL to be ready...")
    import time
    time.sleep(5)
    
    # Setup tables
    if not setup_postgres_tables():
        print("⚠️  Table setup failed, but you can try running the chat service anyway")
    
    print("\n🎉 Setup completed!")
    print("💡 You can now run: python src/services/chat_service.py")
    print("🛑 To stop PostgreSQL: docker stop langgraph-postgres")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
